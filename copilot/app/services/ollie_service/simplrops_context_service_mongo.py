import json
import logging
import os
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from urllib.parse import urlparse

import numpy as np
from dotenv import load_dotenv
from langchain.chains import create_history_aware_retriever, create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_community.retrievers.bm25 import BM25Retriever

from langchain_core.documents import Document
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.retrievers import BaseRetriever
from langchain_mongodb import MongoDBAtlasVectorSearch
from langchain_mongodb.retrievers.hybrid_search import MongoDBAtlasHybridSearchRetriever
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from pymongo import MongoClient

from app.core.config import (
    get_ssm_parameters,
    get_database_url,
    initialize_chain_environment,
    extract_api_version_and_model
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configuration parameter names for SSM
REQUIRED_PARAMETERS = [
    "openaikey",
    "embedding_azure_endpoint",
    "ai-model-endpoint"
]

# Environment variables from pod level only
ENV_NAME = os.environ.get("ENVIRONMENT_NAME")
REGION_NAME = os.environ.get("AWS_REGION")

# Validate required environment variables
if not ENV_NAME:
    raise ValueError("ENVIRONMENT_NAME environment variable is required and must be set at pod level")
if not REGION_NAME:
    raise ValueError("AWS_REGION environment variable is required and must be set at pod level")

# Reranking configuration from environment variables
ENABLE_RERANKING = os.environ.get("ENABLE_RERANKING", "true").lower() == "true"
RERANKING_FETCH_K = int(os.environ.get("RERANKING_FETCH_K", "20"))
RERANKING_RETURN_K = int(os.environ.get("RERANKING_RETURN_K", "8"))
RERANKING_THRESHOLD = float(os.environ.get("RERANKING_THRESHOLD", "0.5"))

# Retriever configuration from environment variables
VECTOR_PENALTY = float(os.environ.get("VECTOR_PENALTY", "40.0"))
FULLTEXT_PENALTY = float(os.environ.get("FULLTEXT_PENALTY", "60.0"))
OVERSAMPLING_FACTOR = int(os.environ.get("OVERSAMPLING_FACTOR", "10"))
PRIMARY_THRESHOLD = float(os.environ.get("PRIMARY_THRESHOLD", "0.70"))
FALLBACK_THRESHOLD = float(os.environ.get("FALLBACK_THRESHOLD", "0.65"))
SEARCH_INDEX_NAME = os.environ.get("SEARCH_INDEX_NAME", "vector_index")
COLLECTION_NAME = os.environ.get("COLLECTION_NAME", "SimplropsNewVectordata")

# Chat history with TTL management
class ChatHistoryManager:
    def __init__(self, max_users=10, ttl_minutes=30):
        self.histories = {}
        self.last_accessed = {}
        self.max_users = max_users
        self.ttl = timedelta(minutes=ttl_minutes)

    def get(self, user_id):
        """Get chat history for a user, initializing if needed"""
        self._cleanup_old_histories()
        if user_id not in self.histories:
            self.histories[user_id] = []
        self.last_accessed[user_id] = datetime.now()
        return self.histories[user_id]

    def update(self, user_id, history):
        """Update chat history for a user"""
        self.histories[user_id] = history
        self.last_accessed[user_id] = datetime.now()

    def _cleanup_old_histories(self):
        """Remove old chat histories based on TTL and max users"""
        now = datetime.now()
        # Remove expired histories
        expired_users = [
            user_id for user_id, last_access in self.last_accessed.items()
            if now - last_access > self.ttl
        ]
        for user_id in expired_users:
            if user_id in self.histories:
                del self.histories[user_id]
                del self.last_accessed[user_id]
                logger.info(f"Removed expired chat history for user {user_id}")

        # If still over limit, remove oldest
        if len(self.histories) > self.max_users:
            oldest_users = sorted(
                self.last_accessed.items(),
                key=lambda x: x[1]
            )[:len(self.histories) - self.max_users]
            for user_id, _ in oldest_users:
                del self.histories[user_id]
                del self.last_accessed[user_id]
                logger.info(f"Removed oldest chat history for user {user_id}")

# Initialize chat history manager with environment variables
CHAT_HISTORY_MAX_USERS = int(os.environ.get("CHAT_HISTORY_MAX_USERS", "10"))
CHAT_HISTORY_TTL_MINUTES = int(os.environ.get("CHAT_HISTORY_TTL_MINUTES", "30"))
chat_history_manager = ChatHistoryManager(max_users=CHAT_HISTORY_MAX_USERS, ttl_minutes=CHAT_HISTORY_TTL_MINUTES)

# Configuration initialization with robust error handling
def initialize_configuration():
    """Initialize configuration using the established patterns from app.core.config"""

    # Log environment information
    logger.info(f"Environment name: {ENV_NAME}")
    logger.info(f"Region name: {REGION_NAME}")

    # Method 1: Try to use the established initialize_chain_environment function
    try:
        logger.info("Attempting to initialize using initialize_chain_environment...")
        llm, embeddings = initialize_chain_environment(REQUIRED_PARAMETERS)
        logger.info("Successfully initialized LLM and embeddings using initialize_chain_environment")
        return llm, embeddings, None  # Return None for params since they're handled internally
    except Exception as e:
        logger.warning(f"initialize_chain_environment failed: {str(e)}. Falling back to manual configuration.")

    # Method 2: Fallback to manual SSM parameter retrieval
    try:
        logger.info("Attempting manual SSM parameter retrieval...")
        params = get_ssm_parameters(REQUIRED_PARAMETERS)
        logger.info("Successfully retrieved parameters from SSM")

        # Parse endpoints using the established extract_api_version_and_model function
        embedding_deployment, embedding_version = extract_api_version_and_model(
            params["embedding_azure_endpoint"]
        )
        ai_model_deployment, ai_model_version = extract_api_version_and_model(
            params["ai-model-endpoint"]
        )

        # Extract Azure base URL
        azure_base_url = "https://" + params["embedding_azure_endpoint"].split("/")[2]

        # Initialize components manually
        embeddings = AzureOpenAIEmbeddings(
            azure_deployment=embedding_deployment,
            openai_api_version=embedding_version,
            openai_api_key=params["openaikey"],
            azure_endpoint=azure_base_url,
        )

        llm = AzureChatOpenAI(
            azure_deployment=ai_model_deployment,
            openai_api_version=ai_model_version,
            openai_api_key=params["openaikey"],
            azure_endpoint=azure_base_url,
            temperature=0.3,
        )

        logger.info(f"Using embedding deployment: {embedding_deployment}, version: {embedding_version}")
        logger.info(f"Using AI model deployment: {ai_model_deployment}, version: {ai_model_version}")

        return llm, embeddings, params

    except Exception as e:
        logger.warning(f"Manual SSM retrieval failed: {str(e)}. Using fallback defaults.")

    # No static fallbacks - configuration must come from SSM or environment
    logger.error("All configuration methods failed. No static fallbacks available.")
    logger.error("Ensure that:")
    logger.error("1. ENVIRONMENT_NAME and AWS_REGION are set at pod level")
    logger.error("2. SSM parameters are properly configured for the environment")
    logger.error("3. AWS credentials are available for SSM access")
    raise RuntimeError(
        f"Unable to initialize configuration. Environment: {ENV_NAME}, Region: {REGION_NAME}. "
        "All configuration retrieval methods failed and no static fallbacks are available."
    )

# Get database URL using the established pattern
def get_mongodb_url():
    """Get MongoDB URL using the established configuration hierarchy - no static fallbacks"""
    try:
        # Use the established get_database_url function
        db_url = get_database_url()
        if db_url:
            logger.info("Successfully retrieved database URL using get_database_url()")
            return db_url
    except Exception as e:
        logger.warning(f"get_database_url() failed: {str(e)}. Trying environment variable.")

    # Fallback to environment variable
    db_url = os.getenv("LOCAL_DB_URL")
    if db_url:
        logger.info("Using LOCAL_DB_URL environment variable")
        return db_url

    # No static fallbacks - must be configured properly
    logger.error("Database URL configuration failed. No static fallbacks available.")
    logger.error("Ensure that:")
    logger.error("1. SSM parameter for database URL is configured")
    logger.error("2. LOCAL_DB_URL environment variable is set, OR")
    logger.error("3. get_database_url() function can retrieve the URL from SSM")
    raise RuntimeError(
        f"Unable to retrieve database URL. Environment: {ENV_NAME}, Region: {REGION_NAME}. "
        "All database URL retrieval methods failed and no static fallbacks are available."
    )

# Initialize configuration
llm, embeddings, params = initialize_configuration()

# Get database URL and parse database name
db_url = get_mongodb_url()
try:
    parsed_url = urlparse(db_url)
    database_name = parsed_url.path.lstrip("/")
    if not database_name:
        logger.error("Could not parse database name from URL and no static fallbacks available.")
        logger.error(f"URL: {db_url}")
        logger.error("Ensure the database URL includes the database name in the path.")
        raise ValueError(f"Database name could not be parsed from URL: {db_url}")
except Exception as e:
    logger.error(f"Error parsing database URL: {str(e)}")
    logger.error("Database URL parsing failed and no static fallbacks are available.")
    raise RuntimeError(f"Unable to parse database name from URL: {db_url}")

logger.info(f"Using database: {database_name}")
logger.info(f"Database URL: {db_url}")


custom_rag_prompt = """You are an expert assistant for SimplrOps, a powerful operations management platform. Your task is to provide accurate, helpful, and well-structured responses based on the provided documentation and never provide PII information in response.

        **Core Guidelines:**
        1. ACCURACY: Your primary goal is to provide accurate information. Only use information that is explicitly present in the provided passages.
        2. CLARITY: Structure responses clearly using HTML for optimal readability.
        3. COMPLETENESS: Address all aspects of the question comprehensively.
        4. RELEVANCE: Stay focused on the specific question asked.

        **Response Structure:**
        1. Start with a clear, direct answer to the main question
        2. Provide step-by-step instructions when applicable
        3. Include relevant URLs in the correct format
        4. Add helpful context without being verbose

        **HTML Formatting Rules:**
        - Use <p> for paragraphs
        - Use <strong> for important points and headings
        - Use <em> for emphasis
        - Use <ul> and <li> for lists
        - Format URLs as: <a href='url'>descriptive text</a>
        - Never use trailing slashes in URLs

        **URL Guidelines:**
        1. Always use the provided BASE_URL
        2. Format: <a href='BASE_URL/#/path'>descriptive text</a>
        3. Common patterns:
           - Main pages: BASE_URL/#/section-name
           - Sub-pages: BASE_URL/#/section/sub-section

        **Response Quality Checklist:**
        1. Is the information accurate and from the provided context?
        2. Is the HTML formatting correct and consistent?
        3. Are all URLs properly formatted?
        4. Is the response clear and well-structured?
        5. Have you avoided any confidentiality notices or references?
        6. Is the tone professional and helpful?

        Remember: You are the expert guide to SimplrOps. Focus on providing practical, actionable information that helps users accomplish their tasks efficiently.

        QUESTION: {input}
        BASE_URL: {url}
        PASSAGE: {context}

        **Reference Format Example:**
        <p><strong></strong></p>
        <p>The key point is <em>[Key Point]</em>. This means [Explanation]</p>
        <ul>
            <!-- For list items -->
            <li>
                <strong>Detail 1:</strong> [Detail Explanation]
                <a href='url1'>URL for the description page</a>
            </li>

            <li>
                <strong>Detail 2:</strong> [Detail Explanation]
                <a href='url2'>URL for the described path</a>
            </li>
            <!-- For URLs -->
            <a href='url1'>URL1</a>
            <a href='url2'>URL2</a>
        </ul>
        <p>If you need further clarification, please let me know!</p>

        **Example of Corrected Code:**
        <a href='https://dev-customer1.simplrops.com/#/home'>Home Page</a>
        <a href='https://dev-customer1.simplrops.com/#/digital-configuration-inventory/dataSetup'>Data Setup Page</a>

        **Note:** Don't put a `/` or `\` in the anchor tag.
        """

contextualize_q_system_prompt = (
    "Given a chat history and the latest user question "
    "which might reference context in the chat history, "
    "formulate a standalone question which can be understood "
    "without the chat history. Do NOT answer the question, "
    "just reformulate it if needed and otherwise return it as is."
)

# Create a custom hybrid retriever that combines multiple retrieval methods
class RobustHybridRetriever(BaseRetriever):
    """A robust hybrid retriever that combines multiple retrieval methods with fallbacks and reranking."""

    def __init__(
        self,
        vector_store: MongoDBAtlasVectorSearch,
        mongo_client: MongoClient,
        collection_name: str,
        embeddings,
        k: int = 8,
        enable_reranking: bool = ENABLE_RERANKING,
        reranking_fetch_k: int = RERANKING_FETCH_K,
        reranking_return_k: int = RERANKING_RETURN_K,
        reranking_threshold: float = RERANKING_THRESHOLD
    ):
        """Initialize the robust hybrid retriever with reranking capabilities.

        Args:
            vector_store: MongoDB Atlas vector store
            mongo_client: MongoDB client
            collection_name: Name of the collection
            embeddings: Embeddings model
            k: Number of documents to return
            enable_reranking: Whether to enable reranking
            reranking_fetch_k: Number of documents to fetch before reranking
            reranking_return_k: Number of documents to return after reranking
            reranking_threshold: Minimum relevance score for documents after reranking
        """
        super().__init__()
        self.vector_store = vector_store
        self.mongo_client = mongo_client
        self.collection_name = collection_name
        self.embeddings = embeddings
        self.k = k
        self.enable_reranking = enable_reranking
        self.reranking_fetch_k = reranking_fetch_k
        self.reranking_return_k = reranking_return_k
        self.reranking_threshold = reranking_threshold
        self.bm25_retriever = None
        self.primary_retriever = None
        self.fallback_retriever = None
        self._setup_retrievers()

    def _setup_retrievers(self):
        """Set up the primary and fallback retrievers."""
        try:
            # Try to set up the hybrid search retriever
            self.primary_retriever = MongoDBAtlasHybridSearchRetriever(
                vectorstore=self.vector_store,
                search_index_name=SEARCH_INDEX_NAME,
                k=self.reranking_fetch_k if self.enable_reranking else self.k,
                vector_penalty=VECTOR_PENALTY,
                fulltext_penalty=FULLTEXT_PENALTY,
                pre_filter=None,
                post_filter=None,
                oversampling_factor=OVERSAMPLING_FACTOR,
            )
            logger.info("Successfully initialized hybrid search retriever as primary")
        except Exception as e:
            logger.warning(f"Failed to initialize hybrid search: {str(e)}. Using vector search as primary.")
            # Use vector search as primary if hybrid search fails
            self.primary_retriever = self.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={
                    "k": self.reranking_fetch_k if self.enable_reranking else self.k,
                    "threshold": PRIMARY_THRESHOLD,
                    "post_filter_pipeline": [],
                },
            )

        # Set up the vector search as fallback
        self.fallback_retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={
                "k": self.reranking_fetch_k if self.enable_reranking else self.k,
                "threshold": FALLBACK_THRESHOLD,
                "post_filter_pipeline": [],
            },
        )

        # Initialize BM25 retriever as last resort fallback
        try:
            # Get documents from MongoDB for BM25 indexing
            db = self.mongo_client[database_name]
            collection = db[self.collection_name.split(".")[-1]]
            docs = list(collection.find({}, {"_id": 0, "text": 1, "metadata": 1}))

            # Convert to Document objects for BM25Retriever
            documents = [
                Document(
                    page_content=doc.get("text", ""),
                    metadata=doc.get("metadata", {})
                )
                for doc in docs if "text" in doc
            ]

            if documents:
                self.bm25_retriever = BM25Retriever.from_documents(
                    documents=documents,
                    k=self.reranking_fetch_k if self.enable_reranking else self.k
                )
                logger.info(f"Successfully initialized BM25 retriever with {len(documents)} documents")
            else:
                logger.warning("No documents found for BM25 retriever")
        except Exception as e:
            logger.warning(f"Failed to initialize BM25 retriever: {str(e)}")

    def _rerank_documents(self, query: str, docs: List[Document]) -> List[Document]:
        """Rerank documents using relevance scores.

        This implementation uses a simple cosine similarity reranking approach.
        For production, consider using a more sophisticated reranker like
        Cohere's reranker or a cross-encoder model.

        Args:
            query: The query string
            docs: List of documents to rerank

        Returns:
            Reranked list of documents
        """
        if not docs:
            logger.info("RERANKING: No documents to rerank")
            return []

        try:
            # Get query embedding
            logger.info("RERANKING: Computing query embedding")
            start_time = time.time()
            query_embedding = self.embeddings.embed_query(query)
            logger.info(f"RERANKING: Query embedding computed in {time.time() - start_time:.2f} seconds")

            # Get document embeddings and calculate relevance scores
            logger.info(f"RERANKING: Computing similarity scores for {len(docs)} documents")
            doc_scores = []
            embedding_from_metadata_count = 0
            computed_embedding_count = 0

            for i, doc in enumerate(docs):
                # Try to get embedding from metadata if available
                if "embedding" in doc.metadata:
                    doc_embedding = doc.metadata["embedding"]
                    embedding_from_metadata_count += 1
                else:
                    # Otherwise, compute it
                    doc_embedding = self.embeddings.embed_documents([doc.page_content])[0]
                    computed_embedding_count += 1

                # Calculate cosine similarity
                similarity = self._cosine_similarity(query_embedding, doc_embedding)
                doc_scores.append((doc, similarity))

                # Log progress for large document sets
                if len(docs) > 10 and (i+1) % 10 == 0:
                    logger.info(f"RERANKING: Processed {i+1}/{len(docs)} documents")

            logger.info(f"RERANKING: Used {embedding_from_metadata_count} cached embeddings, computed {computed_embedding_count} new embeddings")

            # Sort by score in descending order
            doc_scores.sort(key=lambda x: x[1], reverse=True)

            # Log top scores for debugging
            logger.info("RERANKING: Top 5 similarity scores:")
            for i, (doc, score) in enumerate(doc_scores[:5]):
                content_preview = doc.page_content[:50] + "..." if len(doc.page_content) > 50 else doc.page_content
                logger.info(f"  Rank {i+1}: Score={score:.4f} | {content_preview}")

            # Filter by threshold and limit to k
            filtered_docs = [doc for doc, score in doc_scores if score >= self.reranking_threshold]
            logger.info(f"RERANKING: {len(filtered_docs)}/{len(docs)} documents passed threshold {self.reranking_threshold}")

            result_docs = filtered_docs[:self.reranking_return_k]
            logger.info(f"RERANKING: Returning top {len(result_docs)} documents")

            return result_docs

        except Exception as e:
            logger.warning(f"RERANKING FAILED: {str(e)}. Returning original documents.")
            return docs[:self.k]  # Return original docs limited to k

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        try:
            # Convert to numpy arrays
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)

            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            # Avoid division by zero
            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)
        except Exception as e:
            logger.warning(f"Error calculating cosine similarity: {str(e)}")
            return 0.0

    def _log_search_start(self, query: str) -> str:
        """Log the start of search operation and return truncated query."""
        logger.warning("DEBUG: _get_relevant_documents method called in RobustHybridRetriever")
        logger.warning(f"DEBUG: Query = '{query[:30]}...' if len > 30")

        logger.info("=" * 50)
        truncated_query = query[:50] + "..." if len(query) > 50 else query
        logger.info(f"SEARCH OPERATION STARTED: Query = '{truncated_query}'")
        logger.info("=" * 50)
        return truncated_query

    def _log_search_results(self, docs: List[Document], method: str, duration: float):
        """Log search results for a given method."""
        if docs:
            logger.info(f"SUCCESS: {method.upper()} SEARCH returned {len(docs)} documents in {duration:.2f} seconds")
            for i, doc in enumerate(docs[:3]):  # Show first 3 docs
                content_preview = doc.page_content[:100] + "..." if len(doc.page_content) > 100 else doc.page_content
                logger.info(f"  Doc {i+1}: {content_preview}")
            if len(docs) > 3:
                logger.info(f"  ... and {len(docs) - 3} more documents")
        else:
            logger.warning(f"{method.upper()} SEARCH returned no documents")

    def _try_primary_retriever(self, query: str, **kwargs) -> tuple[List[Document], str]:
        """Try primary retriever (hybrid search)."""
        try:
            logger.info("ATTEMPTING HYBRID SEARCH with MongoDB Atlas Hybrid Search")
            start_time = time.time()
            docs = self.primary_retriever.get_relevant_documents(query, **kwargs)
            end_time = time.time()

            self._log_search_results(docs, "hybrid", end_time - start_time)
            return docs, "hybrid" if docs else "none"
        except Exception as e:
            logger.warning(f"FAILED: HYBRID SEARCH error: {str(e)}")
            return [], "none"

    def _try_fallback_retriever(self, query: str, **kwargs) -> tuple[List[Document], str]:
        """Try fallback vector retriever."""
        try:
            logger.info("ATTEMPTING VECTOR SEARCH as fallback")
            start_time = time.time()
            docs = self.fallback_retriever.get_relevant_documents(query, **kwargs)
            end_time = time.time()

            self._log_search_results(docs, "vector", end_time - start_time)
            return docs, "vector" if docs else "none"
        except Exception as e:
            logger.warning(f"FAILED: VECTOR SEARCH error: {str(e)}")
            return [], "none"

    def _try_bm25_retriever(self, query: str, **kwargs) -> tuple[List[Document], str]:
        """Try BM25 keyword retriever."""
        if not self.bm25_retriever:
            return [], "none"

        try:
            logger.info("ATTEMPTING BM25 KEYWORD SEARCH as last resort")
            start_time = time.time()
            docs = self.bm25_retriever.get_relevant_documents(query, **kwargs)
            end_time = time.time()

            self._log_search_results(docs, "bm25", end_time - start_time)
            return docs, "bm25" if docs else "none"
        except Exception as e:
            logger.warning(f"FAILED: BM25 SEARCH error: {str(e)}")
            return [], "none"

    def _apply_reranking_and_finalize(self, query: str, docs: List[Document], search_method: str) -> List[Document]:
        """Apply reranking if enabled and finalize results."""
        if self.enable_reranking and len(docs) > 1:
            logger.info(f"RERANKING: Starting reranking of {len(docs)} documents from {search_method.upper()} SEARCH")
            start_time = time.time()
            reranked_docs = self._rerank_documents(query, docs)
            end_time = time.time()

            logger.info(f"RERANKING: Completed in {end_time - start_time:.2f} seconds")
            logger.info(f"RERANKING: Returned {len(reranked_docs)} documents after filtering with threshold {self.reranking_threshold}")

            self._log_search_summary(search_method, len(docs), len(reranked_docs), True)
            return reranked_docs
        else:
            result_count = min(len(docs), self.k)
            self._log_search_summary(search_method, len(docs), result_count, False)
            return docs[:self.k]

    def _log_search_summary(self, method: str, retrieved: int, returned: int, with_reranking: bool):
        """Log search operation summary."""
        logger.info("=" * 50)
        reranking_text = "with RERANKING" if with_reranking else "without reranking"
        logger.info(f"SEARCH SUMMARY: Used {method.upper()} SEARCH {reranking_text}")
        logger.info(f"SEARCH SUMMARY: Retrieved {retrieved} docs, returned {returned} docs")
        logger.info("=" * 50)

    def _get_relevant_documents(self, query: str, **kwargs) -> List[Document]:
        """Get relevant documents using multiple retrieval methods with fallbacks and reranking."""
        self._log_search_start(query)

        # Try retrievers in order of preference
        docs, search_method = self._try_primary_retriever(query, **kwargs)

        if not docs:
            docs, search_method = self._try_fallback_retriever(query, **kwargs)

        if not docs:
            docs, search_method = self._try_bm25_retriever(query, **kwargs)

        if not docs:
            logger.error("SEARCH FAILED: All retrieval methods failed, returning empty list")
            return []

        return self._apply_reranking_and_finalize(query, docs, search_method)

# Function to measure retrieval performance
def measure_retrieval_performance(func):
    """Decorator to measure retrieval performance"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Retrieval completed in {duration:.2f} seconds")
        return result
    return wrapper

# Initialize MongoDB vector store
try:
    vector_store = MongoDBAtlasVectorSearch.from_connection_string(
        db_url,
        embedding=embeddings,
        namespace=f"{database_name}.{COLLECTION_NAME}",
        index_name=SEARCH_INDEX_NAME,
    )
    logger.info("Successfully initialized vector store")
except Exception as e:
    logger.error(f"Failed to initialize vector store: {str(e)}")
    raise

# Initialize MongoDB client for BM25 retriever
try:
    mongo_client = MongoClient(db_url)
    logger.info("Successfully initialized MongoDB client")
except Exception as e:
    logger.error(f"Failed to initialize MongoDB client: {str(e)}")
    raise

# Create the robust hybrid retriever with reranking
try:
    # Add debug log to track initialization
    logger.warning("ATTEMPTING TO INITIALIZE ROBUST HYBRID RETRIEVER")

    retriever = RobustHybridRetriever(
        vector_store=vector_store,
        mongo_client=mongo_client,
        collection_name=f"{database_name}.{COLLECTION_NAME}",
        embeddings=embeddings,
        k=RERANKING_RETURN_K,
        enable_reranking=ENABLE_RERANKING,
        reranking_fetch_k=RERANKING_FETCH_K,
        reranking_return_k=RERANKING_RETURN_K,
        reranking_threshold=RERANKING_THRESHOLD
    )

    # Add debug log to confirm successful initialization
    logger.warning(f"SUCCESS: ROBUST HYBRID RETRIEVER INITIALIZED with reranking={ENABLE_RERANKING}")

    # Add debug log to check retriever type
    logger.warning(f"RETRIEVER TYPE: {type(retriever).__name__}")

except Exception as e:
    # Add detailed error logging
    logger.error(f"CRITICAL ERROR: Failed to initialize hybrid retriever: {str(e)}")
    logger.error(f"ERROR TYPE: {type(e).__name__}")
    logger.error("ERROR TRACEBACK: ", exc_info=True)

    # Fallback to simple vector search if hybrid retriever fails
    logger.warning("FALLBACK: Using simple vector search instead of hybrid search")
    retriever = vector_store.as_retriever(
        search_type="similarity",
        search_kwargs={
            "k": RERANKING_RETURN_K,
            "threshold": PRIMARY_THRESHOLD,
            "post_filter_pipeline": [],
        },
    )

    # Add debug log to confirm fallback initialization
    logger.warning(f"FALLBACK RETRIEVER TYPE: {type(retriever).__name__}")


@measure_retrieval_performance
def simplrops_context(instructions, query, user_id, url):
    """
    SimplrOps context-aware question answering using LangChain with hybrid search.

    This function takes in a user query, user ID, and a URL as input and
    returns a response. It uses LangChain to create a contextualized question
    from the user's query and chat history, and then uses this contextualized
    question to search for relevant passages in the database using hybrid search
    (vector + keyword). The passages are then used to generate a response using
    the LangChain RAG chain.

    The chat history for each user is maintained in-memory with TTL management,
    and is used to contextualize the user's question. The chat history is limited
    to the last 6 interactions for better context retention.

    Args:
        instructions (str): System instructions for the LLM.
        query (str): The user's question.
        user_id (str): The user's ID.
        url (str): The URL of the page the user is currently on.

    Returns:
        str: The response to the user's question.
    """

    try:
        # Print a clear separator in logs to make it easier to find new queries
        logger.info("\n" + "*" * 80)
        logger.info(f"NEW QUERY PROCESSING STARTED | User: {user_id}")
        logger.info("*" * 80)

        # Truncate long queries in logs
        truncated_query = query[:100] + "..." if len(query) > 100 else query
        logger.info(f"Query: {truncated_query}")
        logger.info(f"Current URL context: {url}")

        # Get chat history for this user (creates if not exists)
        user_history = chat_history_manager.get(user_id)

        # Clean query
        query = query.strip()
        logger.info(f"Cleaned query: {query}")

        # Define the prompt template for contextualizing questions
        contextualize_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", contextualize_q_system_prompt),
                MessagesPlaceholder("chat_history"),
                ("human", "{input}"),
            ]
        )

        # Add debug log before creating history-aware retriever
        logger.warning(f"DEBUG: Creating history-aware retriever with base retriever type: {type(retriever).__name__}")

        # Create a history-aware retriever
        history_aware_retriever = create_history_aware_retriever(
            llm, retriever, contextualize_q_prompt
        )

        # Add debug log after creating history-aware retriever
        logger.warning(f"DEBUG: Created history-aware retriever with type: {type(history_aware_retriever).__name__}")

        # Define the prompt template for question answering
        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", instructions),
                MessagesPlaceholder("chat_history"),
                ("human", "{input}"),
            ]
        )

        # Create the question-answering chain
        question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)

        # Create the retrieval-augmented generation (RAG) chain
        rag_chain = create_retrieval_chain(
            history_aware_retriever, question_answer_chain
        )

        # Log retrieval process
        logger.info("Starting RAG chain invocation")
        logger.info(f"Current chat history length: {len(user_history)}")

        # Call the RAG chain with input query and chat history
        data = rag_chain.invoke(
            {"input": query, "chat_history": user_history, "url": url}
        )

        # Log retrieved documents and answer
        if "source_documents" in data:
            logger.info(f"Retrieved {len(data['source_documents'])} documents")
            for i, doc in enumerate(data["source_documents"]):
                logger.info(f"Document {i+1} metadata: {json.dumps(doc.metadata)}")

        logger.info("Generated answer length: %d characters", len(data["answer"]))
        logger.debug(f"Generated answer: {data['answer']}")

        # Maintain only the last 6 interactions in chat history
        if len(user_history) > 12:  # 6 interactions = 12 messages (human + AI)
            logger.info("Trimming chat history to last 6 interactions")
            user_history = user_history[-12:]

        # Add new interaction to chat history
        user_history.extend(
            [HumanMessage(content=query), AIMessage(content=data["answer"])]
        )
        logger.info(f"Updated chat history length: {len(user_history)}")

        # Update the chat history in the manager
        chat_history_manager.update(user_id, user_history)

        return data["answer"]

    except KeyError as e:
        error_msg = f"KeyError: {e} - Ensure user_id exists and chat history is initialized properly."
        logger.error(error_msg)
        raise
    except NameError as e:
        error_msg = f"NameError: {e} - Check if all variables like 'retriever', 'llm', etc., are defined."
        logger.error(error_msg)
        raise
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise
